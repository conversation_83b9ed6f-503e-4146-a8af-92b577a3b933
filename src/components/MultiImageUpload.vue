<template>
  <div class="multi-image-upload">
    <div class="upload-header">
      <h3>{{ $t('personalPhotos') }}</h3>
      <p class="upload-hint">{{ $t('photoUploadHint') }}</p>
    </div>

    <!-- 已上传的图片预览 -->
    <div v-if="uploadedImages.length > 0" class="uploaded-images">
      <div
        v-for="(image, index) in uploadedImages"
        :key="index"
        class="image-item"
      >
        <img :src="image.preview" :alt="`Photo ${index + 1}`" />
        <button
          type="button"
          class="remove-btn"
          @click="removeImage(index)"
          :title="$t('removePhoto')"
        >
          ×
        </button>
        <div class="image-index">{{ index + 1 }}</div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div
      v-if="uploadedImages.length < maxImages"
      class="upload-area"
      @click="triggerFileInput"
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
      @drop.prevent="onDrop"
      :class="{ 'drag-over': isDragging }"
    >
      <input
        type="file"
        ref="fileInput"
        @change="onFileChange"
        accept="image/*"
        multiple
        class="file-input"
      />

      <div class="upload-placeholder">
        <i class="upload-icon">📷</i>
        <p>{{ $t('dragOrClickMultiple') }}</p>
        <p class="upload-count">
          {{ $t('uploadedCount', { current: uploadedImages.length, min: minImages, max: maxImages }) }}
        </p>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="uploadProgress > 0 && uploadProgress < 100" class="progress-bar">
      <div class="progress" :style="{ width: `${uploadProgress}%` }"></div>
    </div>

    <!-- 错误信息 -->
    <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>

    <!-- 验证提示 -->
    <div class="validation-info">
      <p v-if="uploadedImages.length < minImages" class="warning">
        {{ $t('minPhotosRequired', { min: minImages }) }}
      </p>
      <p v-else-if="uploadedImages.length >= minImages" class="success">
        {{ $t('photosValid') }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  minImages: {
    type: Number,
    default: 3
  },
  maxImages: {
    type: Number,
    default: 5
  },
  maxSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB
  }
});

const emit = defineEmits(['upload-success', 'upload-error', 'validation-change']);

const fileInput = ref(null);
const uploadedImages = ref([]);
const isDragging = ref(false);
const uploadProgress = ref(0);
const errorMessage = ref('');

// 计算是否满足最小数量要求
const isValid = computed(() => uploadedImages.value.length >= props.minImages);

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click();
};

// 拖拽事件处理
const onDragOver = () => {
  isDragging.value = true;
};

const onDragLeave = () => {
  isDragging.value = false;
};

const onDrop = (e) => {
  isDragging.value = false;
  const files = Array.from(e.dataTransfer.files);
  processFiles(files);
};

// 文件选择事件
const onFileChange = (e) => {
  const files = Array.from(e.target.files);
  processFiles(files);
  // 清空input，允许重复选择同一文件
  e.target.value = '';
};

// 处理多个文件
const processFiles = (files) => {
  const remainingSlots = props.maxImages - uploadedImages.value.length;
  
  if (files.length > remainingSlots) {
    errorMessage.value = t('tooManyFiles', { max: remainingSlots });
    return;
  }

  files.forEach(file => processFile(file));
};

// 处理单个文件
const processFile = (file) => {
  // 检查文件类型
  if (!file.type.match('image.*')) {
    errorMessage.value = t('onlyImageAllowed');
    return;
  }

  // 检查文件大小
  if (file.size > props.maxSize) {
    errorMessage.value = t('fileTooLarge', { size: props.maxSize / (1024 * 1024) });
    return;
  }

  errorMessage.value = '';

  // 创建预览
  const reader = new FileReader();
  reader.onload = (e) => {
    const imageData = {
      file: file,
      preview: e.target.result,
      name: file.name,
      size: file.size
    };
    
    uploadedImages.value.push(imageData);
    simulateUpload();
    
    // 发出验证状态变化事件
    emit('validation-change', isValid.value);
    
    // 如果达到最小要求，发出成功事件
    if (isValid.value) {
      emit('upload-success', uploadedImages.value);
    }
  };
  reader.readAsDataURL(file);
};

// 模拟上传进度
const simulateUpload = () => {
  uploadProgress.value = 0;

  const interval = setInterval(() => {
    uploadProgress.value += 20;

    if (uploadProgress.value >= 100) {
      clearInterval(interval);
      uploadProgress.value = 0;
    }
  }, 100);
};

// 移除图片
const removeImage = (index) => {
  uploadedImages.value.splice(index, 1);
  
  // 发出验证状态变化事件
  emit('validation-change', isValid.value);
  
  // 更新上传成功事件
  if (isValid.value) {
    emit('upload-success', uploadedImages.value);
  } else {
    emit('upload-success', []);
  }
};

// 获取上传的图片数据
const getUploadedImages = () => {
  return uploadedImages.value;
};

// 暴露方法给父组件
defineExpose({
  getUploadedImages,
  isValid
});
</script>

<style scoped>
.multi-image-upload {
  width: 100%;
}

.upload-header {
  margin-bottom: 15px;
}

.upload-header h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.upload-hint {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.uploaded-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.image-item {
  position: relative;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  aspect-ratio: 1;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 77, 79, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 16px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background-color: #ff4d4f;
}

.image-index {
  position: absolute;
  bottom: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.02);
}

.drag-over {
  border-color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.05);
}

.file-input {
  display: none;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.upload-count {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

.progress-bar {
  margin: 10px 0;
  height: 6px;
  background-color: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s;
}

.error-message {
  color: #ff4d4f;
  margin: 10px 0;
  font-size: 14px;
}

.validation-info {
  margin-top: 10px;
}

.validation-info .warning {
  color: #ff9800;
  font-size: 14px;
  margin: 0;
}

.validation-info .success {
  color: #4CAF50;
  font-size: 14px;
  margin: 0;
}
</style>
