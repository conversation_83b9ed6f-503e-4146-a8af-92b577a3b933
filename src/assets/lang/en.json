{"register": "Register", "login": "<PERSON><PERSON>", "language": "Language", "selectLanguage": "Select Language", "username": "Username", "email": "Email", "password": "Password", "avatar": "Personal Photos", "personalPhotos": "Personal Photos", "photoUploadHint": "Please upload 3-5 personal photos for identity verification", "dragOrClickMultiple": "Drag multiple images here, or click to select", "uploadedCount": "Uploaded {current} photos, need {min}-{max} photos", "minPhotosRequired": "At least {min} photos are required", "photosValid": "Photo count meets requirements", "removePhoto": "Remove photo", "tooManyFiles": "Can only upload {max} more photos", "submit": "Submit", "submitting": "Submitting...", "loggingIn": "Logging in...", "registerFailed": "Registration failed, please try again", "loginFailed": "<PERSON><PERSON> failed, please check your username and password", "noAccount": "No account? Register now", "languageHint": "Choose your preferred language, which will be used as the default display language", "home": "Home", "report": "Report", "dragOrClick": "Drag an image here, or click to upload", "onlyImageAllowed": "Only image files are allowed", "fileTooLarge": "File is too large, maximum allowed is {size}MB", "adminDashboard": "Admin Dashboard", "userManagement": "User Management", "totalUsers": "Total Users", "activeUsers": "Active Users", "totalReports": "Total Reports", "recentActivity": "Recent Activity", "noRecentActivity": "No recent activity", "searchUsers": "Search users", "addUser": "Add User", "editUser": "Edit User", "role": "Role", "status": "Status", "actions": "Actions", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "confirmDelete": "Confirm Delete", "deleteUserConfirm": "Are you sure you want to delete user {username}?", "active": "Active", "inactive": "Inactive", "user": "User", "admin": "Admin", "addToHomeScreen": "Add to Home Screen", "addToHomeScreenDesc": "Add this app to your home screen for quick access", "install": "Install", "installing": "Installing...", "later": "Later", "addToHomeScreenIOS": "Add to Home Screen", "iosStep1": "Tap the share button at the bottom of the browser", "iosStep2": "Select 'Add to Home Screen' from the popup menu", "iosStep3": "Tap 'Add' to complete the installation", "gotIt": "Got it", "manualInstallGuide": "Please select 'Add to Home Screen' or 'Install App' from your browser menu", "smartInstall": "Smart Install", "installed": "Installed", "autoInstallTitle": "Auto Install Feature", "autoInstallDesc": "Automatically attempt to install the app when enabled", "enable": "Enable", "detectingEnvironment": "Detecting environment...", "preparingInstall": "Preparing installation...", "processingInstall": "Processing installation...", "installComplete": "Installation complete!", "installFailed": "Installation failed", "preparingIOSInstall": "Preparing iOS installation...", "detectingSafariFeatures": "Detecting Safari features...", "guidingUser": "Guiding user...", "iosSmartGuideTitle": "Smart Install Guide", "iosStep1Title": "Tap Share Button", "iosStep1Desc": "Find the share button in Safari's bottom toolbar", "iosStep2Title": "Select Add to Home Screen", "iosStep2Desc": "Find this option in the share menu", "iosStep3Title": "Confirm Addition", "iosStep3Desc": "Tap the Add button in the top right", "nextStep": "Next Step", "finish": "Finish", "close": "Close", "add": "Add", "tryingAlternativeMethod": "Trying alternative method...", "detectingChromeFeatures": "Detecting Chrome features...", "androidSmartGuideTitle": "Android Install Guide", "androidInstallDesc": "Tap the three-dot menu in Chrome and select Install App", "installApp": "Install App", "alternativeInstallTitle": "Alternative Install Method", "alternativeInstallDesc": "Please manually select install option from browser menu", "understand": "I understand", "addToDesktop": "Add to Desktop", "welcomeSubtitle": "Welcome to QuickDock", "quickAccess": "Quick Access", "quickAccessDesc": "Add to desktop with one click, launch quickly anytime, anywhere", "multiLanguage": "Multi-language Support", "multiLanguageDesc": "Supports Chinese, English, Japanese and many other languages", "mobileFirst": "Mobile First", "mobileFirstDesc": "Optimized for mobile devices with smooth experience", "installedSuccess": "Added to Desktop", "installedSuccessDesc": "You can quickly access the app through the desktop icon"}