<template>
  <div class="register-container">
    <h1>{{ $t('register') }}</h1>

    <form @submit.prevent="handleRegister">
      <!-- 用户注册表单字段 -->
      <div class="form-group">
        <label for="username">{{ $t('username') }}</label>
        <input
          type="text"
          id="username"
          v-model="formData.username"
          required
        />
      </div>

      <div class="form-group">
        <label for="email">{{ $t('email') }}</label>
        <input
          type="email"
          id="email"
          v-model="formData.email"
          required
        />
      </div>

      <div class="form-group">
        <label for="password">{{ $t('password') }}</label>
        <input
          type="password"
          id="password"
          v-model="formData.password"
          required
        />
      </div>

      <!-- 语言选择 -->
      <div class="form-group">
        <label for="language">{{ $t('language') }}</label>
        <select
          id="language"
          v-model="selectedLang"
          @change="changeLang"
        >
          <option value="zh">中文</option>
          <option value="en">English</option>
          <option value="th">ไทย</option>
          <option value="ko">한국어</option>
          <option value="ru">Русский</option>
          <option value="ja">日本語</option>
        </select>
        <p class="language-hint">{{ $t('languageHint') }}</p>
      </div>

      <!-- 个人照片上传 -->
      <div class="form-group">
        <MultiImageUpload
          :min-images="3"
          :max-images="5"
          @upload-success="handlePhotosUpload"
          @validation-change="handlePhotosValidation"
          ref="photoUploadRef"
        />
      </div>

      <button type="submit" :disabled="isSubmitting">
        {{ isSubmitting ? $t('submitting') : $t('submit') }}
      </button>
    </form>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '../stores/user';
import ImageUpload from '../components/ImageUpload.vue';
import api from '@/api/user';
import { handleError } from '@/utils/errorHandler';

const { locale, t } = useI18n();
const router = useRouter();
const userStore = useUserStore();

// 表单数据
const formData = ref({
  username: '',
  email: '',
  password: '',
  avatar: '',
  language: locale.value
});

const selectedLang = ref(locale.value);
const isSubmitting = ref(false);

// 切换语言
const changeLang = () => {
  locale.value = selectedLang.value;
  localStorage.setItem('appLang', selectedLang.value);
  formData.value.language = selectedLang.value;
};

// 处理头像上传
const handleAvatarUpload = (imageUrl) => {
  formData.value.avatar = imageUrl;
};

// 处理注册提交
const handleRegister = async () => {
  try {
    isSubmitting.value = true;

    // 调用注册API
    const response = await api.register({
      ...formData.value,
      language: selectedLang.value // 确保语言设置正确
    });

    // 注册成功后自动登录
    if (response.code === 200) {
      // 保存语言设置
      userStore.setLanguage(selectedLang.value);

      // 登录
      await userStore.login({
        username: formData.value.username,
        password: formData.value.password
      });

      // 跳转到首页
      router.push('/');
    }
  } catch (error) {
    const processedError = handleError(error);
    alert(processedError.message || t('registerFailed'));
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.register-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

input, select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.language-hint {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}
</style>
